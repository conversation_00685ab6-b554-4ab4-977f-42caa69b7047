#!/usr/bin/env python3
"""
Test script cho MD5 Analyzer
"""

from md5_analyzer import MD5Analyzer
import hashlib

def test_md5_analyzer():
    """Test các chức năng của MD5Analyzer"""
    analyzer = MD5Analyzer()
    
    print("🧪 Testing MD5 Analyzer...")
    print("=" * 50)
    
    # Test 1: <PERSON><PERSON><PERSON> tra MD5 hợp lệ
    print("\n1. Testing MD5 validation:")
    valid_md5 = "5d41402abc4b2a76b9719d911017c592"
    invalid_md5 = "invalid_hash"
    
    print(f"   Valid MD5 '{valid_md5}': {analyzer.is_valid_md5(valid_md5)}")
    print(f"   Invalid MD5 '{invalid_md5}': {analyzer.is_valid_md5(invalid_md5)}")
    
    # Test 2: Tạo một số MD5 để test
    print("\n2. Testing predictions with sample MD5s:")
    test_strings = ["hello", "world", "telegram", "bot", "test123"]
    
    for test_str in test_strings:
        md5_hash = hashlib.md5(test_str.encode()).hexdigest()
        try:
            prediction, confidence, analysis = analyzer.predict_tai_xiu(md5_hash)
            print(f"   '{test_str}' -> {md5_hash}")
            print(f"   Prediction: {prediction} ({confidence}%)")
            print(f"   Score: {analysis['score']}/7")
            print()
        except Exception as e:
            print(f"   Error analyzing '{test_str}': {e}")
    
    # Test 3: Test với MD5 cụ thể
    print("\n3. Detailed analysis example:")
    sample_md5 = "098f6bcd4621d373cade4e832627b4f6"  # MD5 của "test"
    try:
        prediction, confidence, analysis = analyzer.predict_tai_xiu(sample_md5)
        print(f"   MD5: {sample_md5}")
        print(f"   Prediction: {prediction}")
        print(f"   Confidence: {confidence}%")
        print(f"   Details: {analyzer.get_analysis_details(analysis)}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n✅ Testing completed!")

def generate_sample_md5s():
    """Tạo một số MD5 mẫu để test"""
    print("\n📝 Sample MD5 hashes for testing:")
    print("=" * 50)
    
    samples = [
        "hello world",
        "telegram bot",
        "md5 hash test",
        "tai xiu prediction",
        "random string 123"
    ]
    
    for sample in samples:
        md5_hash = hashlib.md5(sample.encode()).hexdigest()
        print(f"'{sample}' -> {md5_hash}")

if __name__ == "__main__":
    test_md5_analyzer()
    generate_sample_md5s()
