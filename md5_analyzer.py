import hashlib
import random
import re
from typing import Tuple, Dict

class MD5Analyzer:
    """Lớ<PERSON> phân tích chuỗi MD5 để dự đoán <PERSON>/Xỉu"""
    
    def __init__(self, seed: int = 42):
        self.seed = seed
        random.seed(seed)
    
    def is_valid_md5(self, md5_string: str) -> bool:
        """Kiểm tra chuỗi MD5 có hợp lệ không"""
        if not md5_string:
            return False
        
        # MD5 phải có độ dài 32 ký tự và chỉ chứa hex
        pattern = r'^[a-fA-F0-9]{32}$'
        return bool(re.match(pattern, md5_string))
    
    def calculate_hash_properties(self, md5_string: str) -> Dict:
        """Tính toán các thuộc tính của hash để phân tích"""
        md5_string = md5_string.lower()
        
        # Tính tổng giá trị hex
        hex_sum = sum(int(char, 16) for char in md5_string)
        
        # Đế<PERSON> số ký tự chẵn/lẻ
        even_count = sum(1 for char in md5_string if int(char, 16) % 2 == 0)
        odd_count = 32 - even_count
        
        # Tính tổng vị trí của các ký tự
        position_sum = sum(i * int(char, 16) for i, char in enumerate(md5_string))
        
        # Đếm các ký tự đặc biệt (a-f)
        letter_count = sum(1 for char in md5_string if char in 'abcdef')
        number_count = 32 - letter_count
        
        return {
            'hex_sum': hex_sum,
            'even_count': even_count,
            'odd_count': odd_count,
            'position_sum': position_sum,
            'letter_count': letter_count,
            'number_count': number_count,
            'first_char': int(md5_string[0], 16),
            'last_char': int(md5_string[-1], 16),
            'middle_chars': int(md5_string[15:17], 16)
        }
    
    def predict_tai_xiu(self, md5_string: str) -> Tuple[str, float, Dict]:
        """
        Dự đoán Tài/Xỉu dựa trên phân tích MD5
        Returns: (prediction, confidence, analysis_data)
        """
        if not self.is_valid_md5(md5_string):
            raise ValueError("Chuỗi MD5 không hợp lệ!")
        
        properties = self.calculate_hash_properties(md5_string)
        
        # Thuật toán dự đoán dựa trên các yếu tố
        score = 0
        
        # Yếu tố 1: Tổng hex
        if properties['hex_sum'] % 2 == 0:
            score += 1
        
        # Yếu tố 2: Số ký tự chẵn vs lẻ
        if properties['even_count'] > properties['odd_count']:
            score += 1
        
        # Yếu tố 3: Ký tự đầu
        if properties['first_char'] >= 8:
            score += 1
        
        # Yếu tố 4: Ký tự cuối
        if properties['last_char'] >= 8:
            score += 1
        
        # Yếu tố 5: Ký tự giữa
        if properties['middle_chars'] >= 128:
            score += 1
        
        # Yếu tố 6: Tỷ lệ chữ/số
        if properties['letter_count'] > properties['number_count']:
            score += 1
        
        # Yếu tố 7: Tổng vị trí
        if properties['position_sum'] % 3 == 0:
            score += 1
        
        # Dự đoán dựa trên điểm số
        if score >= 4:
            prediction = "TÀI"
            base_confidence = 55 + (score - 4) * 5
        else:
            prediction = "XỈU"
            base_confidence = 55 + (4 - score) * 5
        
        # Thêm yếu tố ngẫu nhiên để tạo độ tin cậy thực tế
        random.seed(properties['hex_sum'])
        confidence_variation = random.uniform(-10, 15)
        final_confidence = min(95, max(65, base_confidence + confidence_variation))
        
        analysis_data = {
            'properties': properties,
            'score': score,
            'factors_analyzed': 7
        }
        
        return prediction, round(final_confidence, 1), analysis_data
    
    def get_analysis_details(self, analysis_data: Dict) -> str:
        """Tạo mô tả chi tiết về phân tích"""
        props = analysis_data['properties']
        score = analysis_data['score']
        
        details = f"""
📊 **Chi tiết phân tích:**
• Tổng hex: {props['hex_sum']}
• Ký tự chẵn/lẻ: {props['even_count']}/{props['odd_count']}
• Chữ/Số: {props['letter_count']}/{props['number_count']}
• Ký tự đầu: {props['first_char']} (0x{props['first_char']:x})
• Ký tự cuối: {props['last_char']} (0x{props['last_char']:x})
• Điểm phân tích: {score}/7
        """
        
        return details.strip()
