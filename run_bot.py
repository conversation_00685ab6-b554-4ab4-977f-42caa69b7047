#!/usr/bin/env python3
"""
Script chạy bot với các tùy chọn bổ sung
"""

import sys
import os
import asyncio
from telegram_bot import TelegramMD5Bot
from config import TELEGRAM_BOT_TOKEN

def check_requirements():
    """Kiểm tra các yêu cầu cần thiết"""
    try:
        import telegram
        from telegram.ext import Application
        print("✅ python-telegram-bot đã được cài đặt")
    except ImportError:
        print("❌ Chưa cài đặt python-telegram-bot")
        print("Chạy: pip install -r requirements.txt")
        return False
    
    if TELEGRAM_BOT_TOKEN == 'YOUR_BOT_TOKEN_HERE':
        print("❌ Chưa cấu hình Bot Token")
        print("Hướng dẫn:")
        print("1. Tạo bot với @BotFather trên Telegram")
        print("2. Sửa TELEGRAM_BOT_TOKEN trong config.py")
        print("3. Hoặc set biến môi trường TELEGRAM_BOT_TOKEN")
        return False
    
    print("✅ Bot Token đã được cấu hình")
    return True

def main():
    """Hàm main"""
    print("🤖 Telegram MD5 Tài Xỉu Bot")
    print("=" * 40)
    
    # Kiểm tra yêu cầu
    if not check_requirements():
        sys.exit(1)
    
    # Tạo bot
    bot = TelegramMD5Bot(TELEGRAM_BOT_TOKEN)
    
    print(f"🚀 Đang khởi động bot...")
    print(f"📱 Token: {TELEGRAM_BOT_TOKEN[:10]}...")
    print("🔄 Nhấn Ctrl+C để dừng bot")
    print("=" * 40)
    
    try:
        # Chạy bot
        asyncio.run(bot.run())
    except KeyboardInterrupt:
        print("\n👋 Bot đã dừng!")
    except Exception as e:
        print(f"\n❌ Lỗi: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
