# Cấu hình cho Telegram Bot
import os

# Token bot Telegram - thay thế bằng token thực của bạn
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN_HERE')

# Cấu hình dự đoán
PREDICTION_ACCURACY_RANGE = (75, 95)  # Phạm vi độ chính xác ngẫu nhiên
HASH_ANALYSIS_SEED = 42  # Seed cho tính nhất quán

# Thông điệp
WELCOME_MESSAGE = """
🎲 **Bot Dự Đoán Tài Xỉu MD5** 🎲

Gửi cho tôi một chuỗi MD5 và tôi sẽ phân tích để dự đoán kết quả Tài/Xỉu!

Ví dụ: `5d41402abc4b2a76b9719d911017c592`

Lệnh:
/start - Bắt đầu
/help - Hướng dẫn
/analyze <md5> - <PERSON><PERSON> tích MD5
"""

HELP_MESSAGE = """
📖 **Hướng dẫn sử dụng:**

1. <PERSON><PERSON><PERSON> lệnh `/analyze` theo sau bởi chuỗi MD5
2. Bo<PERSON> sẽ phân tích và đưa ra dự đoán Tài/Xỉu
3. Kết quả bao gồm tỷ lệ phần trăm độ tin cậy

**Ví dụ:**
`/analyze 5d41402abc4b2a76b9719d911017c592`

**Lưu ý:** Đây là công cụ giải trí, không đảm bảo độ chính xác tuyệt đối.
"""
