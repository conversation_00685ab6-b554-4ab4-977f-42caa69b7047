# 🎲 Telegram MD5 Tài Xỉu Bot

Bot Telegram phân tích chuỗi MD5 và dự đoán kết quả Tài/Xỉu với độ chính xác được báo cáo.

## ✨ Tính năng

- 🔍 Phân tích chuỗi MD5 hợp lệ
- 🎯 Dự đoán kết quả TÀI/XỈU
- 📊 Báo cáo độ tin cậy (%)
- 📈 Phân tích chi tiết các yếu tố
- 🤖 Giao diện Telegram thân thiện

## 🚀 Cài đặt

### 1. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 2. Tạo Telegram Bot

1. Mở Telegram và tìm `@BotFather`
2. <PERSON><PERSON><PERSON> lệnh `/newbot`
3. Đặt tên cho bot của bạn
4. Lấy token API

### 3. Cấu hình Bot Token

**Cách 1: Sửa file config.py**
```python
TELEGRAM_BOT_TOKEN = "your_bot_token_here"
```

**Cách 2: Sử dụng biến môi trường**
```bash
# Windows
set TELEGRAM_BOT_TOKEN=your_bot_token_here

# Linux/Mac
export TELEGRAM_BOT_TOKEN=your_bot_token_here
```

## 🎮 Sử dụng

### Chạy bot
```bash
python telegram_bot.py
```

### Test analyzer
```bash
python test_analyzer.py
```

## 📱 Lệnh Bot

- `/start` - Bắt đầu sử dụng bot
- `/help` - Xem hướng dẫn
- `/analyze <md5>` - Phân tích MD5 cụ thể
- Gửi trực tiếp chuỗi MD5 (32 ký tự hex)

## 📊 Ví dụ sử dụng

```
User: /analyze 5d41402abc4b2a76b9719d911017c592

Bot: 🔴 KẾT QUẢ DỰ ĐOÁN

🎲 MD5: 5d41402abc4b2a76b9719d911017c592
🎯 Dự đoán: TÀI
📊 Độ tin cậy: 78.5%

📊 Chi tiết phân tích:
• Tổng hex: 456
• Ký tự chẵn/lẻ: 18/14
• Chữ/Số: 12/20
• Ký tự đầu: 5 (0x5)
• Ký tự cuối: 2 (0x2)
• Điểm phân tích: 4/7
```

## 🔧 Cấu trúc dự án

```
├── telegram_bot.py      # Bot Telegram chính
├── md5_analyzer.py      # Logic phân tích MD5
├── config.py           # Cấu hình
├── test_analyzer.py    # Test script
├── requirements.txt    # Dependencies
└── README.md          # Hướng dẫn
```

## 🧮 Thuật toán phân tích

Bot sử dụng 7 yếu tố để phân tích MD5:

1. **Tổng hex**: Tổng giá trị của tất cả ký tự hex
2. **Ký tự chẵn/lẻ**: Tỷ lệ ký tự có giá trị chẵn vs lẻ
3. **Ký tự đầu**: Giá trị ký tự đầu tiên
4. **Ký tự cuối**: Giá trị ký tự cuối cùng
5. **Ký tự giữa**: Giá trị 2 ký tự ở giữa
6. **Tỷ lệ chữ/số**: Tỷ lệ ký tự a-f vs 0-9
7. **Tổng vị trí**: Tổng có trọng số theo vị trí

## ⚠️ Lưu ý

- Đây là công cụ giải trí, không đảm bảo độ chính xác tuyệt đối
- Kết quả chỉ mang tính tham khảo
- Không sử dụng cho mục đích cờ bạc thực tế

## 🛠️ Phát triển

### Chạy tests
```bash
python test_analyzer.py
```

### Thêm tính năng mới
1. Sửa `md5_analyzer.py` để thêm logic phân tích
2. Cập nhật `telegram_bot.py` cho giao diện
3. Test với `test_analyzer.py`

## 📄 License

MIT License - Sử dụng tự do cho mục đích học tập và giải trí.
