import logging
import asyncio
from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from md5_analyzer import MD5A<PERSON>yzer
from config import TELEGRAM_BOT_TOKEN, WELCOME_MESSAGE, HELP_MESSAGE

# <PERSON><PERSON><PERSON> hình logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TelegramMD5Bot:
    """Bot Telegram để phân tích MD5 và dự đoán Tài/Xỉu"""
    
    def __init__(self, token: str):
        self.token = token
        self.analyzer = MD5Analyzer()
        self.application = None
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Xử lý lệnh /start"""
        await update.message.reply_text(
            WELCOME_MESSAGE,
            parse_mode='Markdown'
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Xử lý lệnh /help"""
        await update.message.reply_text(
            HELP_MESSAGE,
            parse_mode='Markdown'
        )
    
    async def analyze_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Xử lý lệnh /analyze"""
        if not context.args:
            await update.message.reply_text(
                "❌ Vui lòng cung cấp chuỗi MD5!\n\n"
                "Ví dụ: `/analyze 5d41402abc4b2a76b9719d911017c592`",
                parse_mode='Markdown'
            )
            return
        
        md5_string = context.args[0]
        await self.process_md5_analysis(update, md5_string)
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Xử lý tin nhắn thường (có thể chứa MD5)"""
        message_text = update.message.text.strip()
        
        # Kiểm tra xem tin nhắn có phải là MD5 không
        if self.analyzer.is_valid_md5(message_text):
            await self.process_md5_analysis(update, message_text)
        else:
            await update.message.reply_text(
                "🤔 Tôi không hiểu tin nhắn này.\n\n"
                "Gửi cho tôi:\n"
                "• Chuỗi MD5 (32 ký tự hex)\n"
                "• Hoặc sử dụng lệnh `/analyze <md5>`\n"
                "• Hoặc `/help` để xem hướng dẫn",
                parse_mode='Markdown'
            )
    
    async def process_md5_analysis(self, update: Update, md5_string: str):
        """Xử lý phân tích MD5"""
        try:
            # Gửi tin nhắn đang xử lý
            processing_msg = await update.message.reply_text("🔄 Đang phân tích MD5...")
            
            # Thực hiện phân tích
            prediction, confidence, analysis_data = self.analyzer.predict_tai_xiu(md5_string)
            
            # Tạo emoji cho kết quả
            result_emoji = "🔴" if prediction == "TÀI" else "🔵"
            confidence_emoji = "🎯" if confidence >= 80 else "📊"
            
            # Tạo thông điệp kết quả
            result_message = f"""
{result_emoji} **KẾT QUẢ DỰ ĐOÁN**

🎲 **MD5:** `{md5_string}`
🎯 **Dự đoán:** **{prediction}**
{confidence_emoji} **Độ tin cậy:** {confidence}%

{self.analyzer.get_analysis_details(analysis_data)}

⚠️ *Lưu ý: Đây là công cụ giải trí, kết quả chỉ mang tính tham khảo.*
            """
            
            # Cập nhật tin nhắn
            await processing_msg.edit_text(
                result_message.strip(),
                parse_mode='Markdown'
            )
            
            # Log kết quả
            logger.info(f"Analyzed MD5: {md5_string} -> {prediction} ({confidence}%)")
            
        except ValueError as e:
            await update.message.reply_text(f"❌ Lỗi: {str(e)}")
        except Exception as e:
            logger.error(f"Error processing MD5 analysis: {e}")
            await update.message.reply_text(
                "❌ Có lỗi xảy ra khi phân tích. Vui lòng thử lại!"
            )
    
    async def error_handler(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Xử lý lỗi"""
        logger.error(f"Update {update} caused error {context.error}")
    
    def setup_handlers(self):
        """Thiết lập các handler cho bot"""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        self.application.add_handler(CommandHandler("analyze", self.analyze_command))
        
        # Message handler
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message)
        )
        
        # Error handler
        self.application.add_error_handler(self.error_handler)
    
    async def run(self):
        """Chạy bot"""
        # Tạo application
        self.application = Application.builder().token(self.token).build()
        
        # Thiết lập handlers
        self.setup_handlers()
        
        # Bắt đầu bot
        logger.info("Starting Telegram MD5 Bot...")
        await self.application.run_polling()

def main():
    """Hàm main để chạy bot"""
    if TELEGRAM_BOT_TOKEN == 'YOUR_BOT_TOKEN_HERE':
        print("❌ Lỗi: Vui lòng cấu hình TELEGRAM_BOT_TOKEN trong config.py hoặc biến môi trường!")
        print("Hướng dẫn:")
        print("1. Tạo bot mới với @BotFather trên Telegram")
        print("2. Lấy token và thay thế trong config.py")
        print("3. Hoặc set biến môi trường: TELEGRAM_BOT_TOKEN=your_token")
        return
    
    # Tạo và chạy bot
    bot = TelegramMD5Bot(TELEGRAM_BOT_TOKEN)
    
    try:
        asyncio.run(bot.run())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Bot error: {e}")

if __name__ == "__main__":
    main()
